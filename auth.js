// Authentication JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeAuthPage();
});

function initializeAuthPage() {
    setupAuthEventListeners();
    setupPasswordToggles();
    setupPasswordStrength();
    setupFormValidation();
}

function setupAuthEventListeners() {
    // Sign in form
    const signinForm = document.getElementById('signinForm');
    if (signinForm) {
        signinForm.addEventListener('submit', handleSignIn);
    }
    
    // Sign up form
    const signupForm = document.getElementById('signupForm');
    if (signupForm) {
        signupForm.addEventListener('submit', handleSignUp);
    }
    
    // Social login buttons
    const socialBtns = document.querySelectorAll('.social-btn');
    socialBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const provider = this.classList.contains('google') ? 'Google' : 'Facebook';
            handleSocialLogin(provider);
        });
    });
    
    // Forgot password link
    const forgotPasswordLink = document.querySelector('.forgot-password');
    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', function(e) {
            e.preventDefault();
            handleForgotPassword();
        });
    }
}

function setupPasswordToggles() {
    const passwordToggles = document.querySelectorAll('.password-toggle');
    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
}

function setupPasswordStrength() {
    const passwordInput = document.getElementById('password');
    if (passwordInput && passwordInput.closest('form').id === 'signupForm') {
        passwordInput.addEventListener('input', function() {
            updatePasswordStrength(this.value);
        });
    }
}

function updatePasswordStrength(password) {
    const strengthBar = document.querySelector('.strength-fill');
    const strengthText = document.querySelector('.strength-text');
    
    if (!strengthBar || !strengthText) return;
    
    let strength = 0;
    let strengthLabel = 'Weak';
    let color = '#ff4757';
    
    // Check password criteria
    if (password.length >= 8) strength += 25;
    if (/[a-z]/.test(password)) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/[0-9]/.test(password)) strength += 25;
    if (/[^A-Za-z0-9]/.test(password)) strength += 25;
    
    // Determine strength level
    if (strength >= 100) {
        strengthLabel = 'Very Strong';
        color = '#2ed573';
    } else if (strength >= 75) {
        strengthLabel = 'Strong';
        color = '#ffa502';
    } else if (strength >= 50) {
        strengthLabel = 'Medium';
        color = '#ff6348';
    } else if (strength >= 25) {
        strengthLabel = 'Weak';
        color = '#ff4757';
    } else {
        strengthLabel = 'Very Weak';
        color = '#ff3838';
    }
    
    // Update UI
    strengthBar.style.width = `${Math.min(strength, 100)}%`;
    strengthBar.style.background = color;
    strengthText.textContent = strengthLabel;
    strengthText.style.color = color;
}

function setupFormValidation() {
    // Real-time validation for email
    const emailInputs = document.querySelectorAll('input[type="email"]');
    emailInputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateEmail(this);
        });
    });
    
    // Password confirmation validation
    const confirmPasswordInput = document.getElementById('confirmPassword');
    if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('blur', function() {
            validatePasswordConfirmation();
        });
    }
    
    // Phone number formatting
    const phoneInput = document.getElementById('phone');
    if (phoneInput) {
        phoneInput.addEventListener('input', function() {
            formatPhoneNumber(this);
        });
    }
}

function validateEmail(input) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(input.value);
    
    if (input.value && !isValid) {
        input.style.borderColor = '#ff4757';
        showFieldError(input, 'Please enter a valid email address');
    } else {
        input.style.borderColor = '#e1e8ed';
        hideFieldError(input);
    }
    
    return isValid;
}

function validatePasswordConfirmation() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirmPassword');
    
    if (!password || !confirmPassword) return true;
    
    const isValid = password.value === confirmPassword.value;
    
    if (confirmPassword.value && !isValid) {
        confirmPassword.style.borderColor = '#ff4757';
        showFieldError(confirmPassword, 'Passwords do not match');
    } else {
        confirmPassword.style.borderColor = '#e1e8ed';
        hideFieldError(confirmPassword);
    }
    
    return isValid;
}

function formatPhoneNumber(input) {
    let value = input.value.replace(/\D/g, '');
    if (value.length >= 6) {
        value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    } else if (value.length >= 3) {
        value = value.replace(/(\d{3})(\d{0,3})/, '($1) $2');
    }
    input.value = value;
}

function showFieldError(input, message) {
    hideFieldError(input); // Remove existing error
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.style.cssText = `
        color: #ff4757;
        font-size: 0.8rem;
        margin-top: 0.25rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    `;
    errorDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
    
    input.parentElement.parentElement.appendChild(errorDiv);
}

function hideFieldError(input) {
    const existingError = input.parentElement.parentElement.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

function handleSignIn(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const email = formData.get('email');
    const password = formData.get('password');
    const rememberMe = formData.get('rememberMe');
    
    // Basic validation
    if (!email || !password) {
        alert('Please fill in all required fields');
        return;
    }
    
    if (!validateEmail(document.getElementById('email'))) {
        return;
    }
    
    // Simulate login process
    showLoadingState(e.target.querySelector('.auth-btn'));
    
    setTimeout(() => {
        hideLoadingState(e.target.querySelector('.auth-btn'));
        
        // In a real app, you would send this to your authentication API
        console.log('Sign in attempt:', { email, rememberMe });
        
        // Simulate successful login
        alert('Sign in successful! Redirecting to your profile...');
        window.location.href = 'profile.html';
    }, 2000);
}

function handleSignUp(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const userData = {
        firstName: formData.get('firstName'),
        lastName: formData.get('lastName'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        password: formData.get('password'),
        confirmPassword: formData.get('confirmPassword'),
        birthDate: formData.get('birthDate'),
        favoriteSport: formData.get('favoritesSport'),
        agreeTerms: formData.get('agreeTerms'),
        newsletter: formData.get('newsletter')
    };
    
    // Validation
    if (!userData.firstName || !userData.lastName || !userData.email || !userData.password) {
        alert('Please fill in all required fields');
        return;
    }
    
    if (!validateEmail(document.getElementById('email'))) {
        return;
    }
    
    if (!validatePasswordConfirmation()) {
        return;
    }
    
    if (!userData.agreeTerms) {
        alert('Please agree to the Terms of Service and Privacy Policy');
        return;
    }
    
    // Simulate registration process
    showLoadingState(e.target.querySelector('.auth-btn'));
    
    setTimeout(() => {
        hideLoadingState(e.target.querySelector('.auth-btn'));
        
        // In a real app, you would send this to your registration API
        console.log('Sign up attempt:', userData);
        
        // Simulate successful registration
        alert('Account created successfully! Please check your email to verify your account.');
        window.location.href = 'signin.html';
    }, 2000);
}

function handleSocialLogin(provider) {
    alert(`${provider} login would be implemented here. This would redirect to ${provider}'s OAuth flow.`);
}

function handleForgotPassword() {
    const email = prompt('Please enter your email address:');
    if (email && validateEmailString(email)) {
        alert('Password reset instructions have been sent to your email address.');
    } else if (email) {
        alert('Please enter a valid email address.');
    }
}

function validateEmailString(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showLoadingState(button) {
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
}

function hideLoadingState(button) {
    button.disabled = false;
    const isSignIn = button.closest('form').id === 'signinForm';
    button.innerHTML = isSignIn 
        ? '<i class="fas fa-sign-in-alt"></i> Sign In'
        : '<i class="fas fa-user-plus"></i> Create Account';
}
