<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping Cart - SportZone</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <i class="fas fa-running"></i>
                <span>SportZone</span>
            </div>
            <button class="sidebar-close" id="sidebarClose">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="sidebar-content">
            <div class="sidebar-section">
                <h3>Browse</h3>
                <ul class="sidebar-menu">
                    <li><a href="index.html" class="sidebar-link"><i class="fas fa-home"></i> Home</a></li>
                    <li><a href="index.html#popular" class="sidebar-link"><i class="fas fa-fire"></i> Most Popular</a></li>
                    <li><a href="index.html#hotsale" class="sidebar-link"><i class="fas fa-percent"></i> Hot Sales</a></li>
                    <li><a href="index.html#new" class="sidebar-link"><i class="fas fa-star"></i> New Arrivals</a></li>
                </ul>
            </div>
            
            <div class="sidebar-section">
                <h3>Sports Categories</h3>
                <ul class="sidebar-menu">
                    <li><a href="index.html" class="sidebar-link" data-category="all"><i class="fas fa-th-large"></i> All Sports</a></li>
                    <li><a href="index.html" class="sidebar-link" data-category="soccer"><i class="fas fa-futbol"></i> Soccer</a></li>
                    <li><a href="index.html" class="sidebar-link" data-category="badminton"><i class="fas fa-table-tennis"></i> Badminton</a></li>
                    <li><a href="index.html" class="sidebar-link" data-category="pingpong"><i class="fas fa-ping-pong-paddle-ball"></i> Ping Pong</a></li>
                    <li><a href="index.html" class="sidebar-link" data-category="basketball"><i class="fas fa-basketball-ball"></i> Basketball</a></li>
                    <li><a href="index.html" class="sidebar-link" data-category="tennis"><i class="fas fa-tennis-ball"></i> Tennis</a></li>
                </ul>
            </div>
            
            <div class="sidebar-section">
                <h3>Account</h3>
                <ul class="sidebar-menu">
                    <li><a href="profile.html" class="sidebar-link"><i class="fas fa-user"></i> Profile</a></li>
                    <li><a href="cart.html" class="sidebar-link active"><i class="fas fa-shopping-cart"></i> Cart <span class="cart-count-sidebar">0</span></a></li>
                    <li><a href="signin.html" class="sidebar-link"><i class="fas fa-sign-in-alt"></i> Sign In</a></li>
                    <li><a href="signup.html" class="sidebar-link"><i class="fas fa-user-plus"></i> Sign Up</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <button class="sidebar-toggle" id="sidebarToggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </button>
            <div class="nav-logo">
                <i class="fas fa-running"></i>
                <span>SportZone</span>
            </div>
            <div class="nav-actions">
                <a href="signin.html" class="nav-signin">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>Sign In</span>
                </a>
                <a href="cart.html" class="nav-cart active">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </a>
                <a href="profile.html" class="nav-profile">
                    <i class="fas fa-user"></i>
                </a>
            </div>
        </div>
    </nav>

    <!-- Cart Page Content -->
    <main class="cart-page">
        <div class="container">
            <div class="page-header">
                <h1><i class="fas fa-shopping-cart"></i> Shopping Cart</h1>
                <p>Review your items and proceed to checkout</p>
            </div>

            <div class="cart-modal-style">
                <div class="cart-modal-content">
                    <div class="cart-modal-header">
                        <h2>Your Items</h2>
                        <button class="clear-all-btn" id="clearAllCart">
                            <i class="fas fa-trash"></i> Clear All
                        </button>
                    </div>

                    <div class="cart-items-container" id="cartItemsList">
                        <!-- Cart items will be populated by JavaScript -->
                    </div>

                    <div class="cart-modal-footer">
                        <div class="cart-summary-inline">
                            <div class="summary-row">
                                <span>Subtotal:</span>
                                <span id="cartSubtotal">$0.00</span>
                            </div>
                            <div class="summary-row">
                                <span>Shipping:</span>
                                <span id="cartShipping">$9.99</span>
                            </div>
                            <div class="summary-row">
                                <span>Tax:</span>
                                <span id="cartTax">$0.00</span>
                            </div>
                            <div class="summary-divider"></div>
                            <div class="summary-row total">
                                <span>Total:</span>
                                <span id="cartTotal">$0.00</span>
                            </div>
                        </div>

                        <div class="cart-actions-inline">
                            <button class="continue-shopping-btn" onclick="window.location.href='index.html'">
                                <i class="fas fa-arrow-left"></i> Continue Shopping
                            </button>
                            <button class="checkout-btn" id="checkoutBtn">
                                <i class="fas fa-credit-card"></i> Proceed to Checkout
                            </button>
                        </div>

                        <div class="promo-section-inline">
                            <h4>Have a promo code?</h4>
                            <div class="promo-input">
                                <input type="text" placeholder="Enter promo code" id="promoCode">
                                <button class="apply-promo-btn">Apply</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Empty Cart State -->
    <div class="empty-cart-state" id="emptyCartState" style="display: none;">
        <div class="container">
            <div class="empty-cart-content">
                <i class="fas fa-shopping-cart"></i>
                <h2>Your cart is empty</h2>
                <p>Looks like you haven't added any items to your cart yet.</p>
                <button class="start-shopping-btn" onclick="window.location.href='index.html'">
                    <i class="fas fa-plus"></i> Start Shopping
                </button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>SportZone</h3>
                    <p>Your trusted partner for all sports equipment and gear.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="cart.html">Cart</a></li>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="signin.html">Sign In</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Email: <EMAIL></p>
                    <p>Phone: (*************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 SportZone. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script src="cart.js"></script>
</body>
</html>
