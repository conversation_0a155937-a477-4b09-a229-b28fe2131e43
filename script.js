// Sports Products Data
const sportsProducts = [
    // Soccer Products
    {
        id: 1,
        name: "Professional Soccer Jersey",
        category: "soccer",
        price: 89.99,
        originalPrice: 119.99,
        description: "High-quality breathable soccer jersey with moisture-wicking technology",
        icon: "fas fa-tshirt",
        onSale: true
    },
    {
        id: 2,
        name: "Goalkeeper Gloves Pro",
        category: "soccer",
        price: 45.99,
        description: "Professional goalkeeper gloves with superior grip and protection",
        icon: "fas fa-hand-paper",
        onSale: false
    },
    {
        id: 3,
        name: "Soccer Cleats Elite",
        category: "soccer",
        price: 129.99,
        originalPrice: 159.99,
        description: "Lightweight soccer cleats for optimal performance on the field",
        icon: "fas fa-shoe-prints",
        onSale: true
    },
    
    // Badminton Products
    {
        id: 4,
        name: "Carbon Fiber Badminton Racket",
        category: "badminton",
        price: 79.99,
        description: "Lightweight carbon fiber racket for professional play",
        icon: "fas fa-table-tennis",
        onSale: false
    },
    {
        id: 5,
        name: "Premium Grip Tape",
        category: "badminton",
        price: 12.99,
        description: "Anti-slip grip tape for better racket control",
        icon: "fas fa-tape",
        onSale: false
    },
    {
        id: 6,
        name: "Feather Shuttlecocks (12 pack)",
        category: "badminton",
        price: 24.99,
        originalPrice: 34.99,
        description: "Tournament-grade feather shuttlecocks for competitive play",
        icon: "fas fa-feather-alt",
        onSale: true
    },
    
    // Ping Pong Products
    {
        id: 7,
        name: "Professional Table Tennis Bat",
        category: "pingpong",
        price: 59.99,
        description: "High-performance ping pong paddle with rubber coating",
        icon: "fas fa-ping-pong-paddle-ball",
        onSale: false
    },
    {
        id: 8,
        name: "Table Tennis Balls (50 pack)",
        category: "pingpong",
        price: 19.99,
        description: "Official size and weight ping pong balls",
        icon: "fas fa-circle",
        onSale: false
    },
    {
        id: 9,
        name: "Portable Table Tennis Net",
        category: "pingpong",
        price: 29.99,
        originalPrice: 39.99,
        description: "Adjustable net that fits any table",
        icon: "fas fa-volleyball-ball",
        onSale: true
    },
    {
        id: 10,
        name: "Professional Ping Pong Table",
        category: "pingpong",
        price: 899.99,
        description: "Tournament-standard table tennis table with wheels",
        icon: "fas fa-table",
        onSale: false
    },
    
    // Basketball Products
    {
        id: 11,
        name: "Basketball Pro",
        category: "basketball",
        price: 39.99,
        description: "Official size basketball with superior grip",
        icon: "fas fa-basketball-ball",
        onSale: false
    },
    {
        id: 12,
        name: "Basketball Shoes Elite",
        category: "basketball",
        price: 149.99,
        originalPrice: 199.99,
        description: "High-top basketball shoes with ankle support",
        icon: "fas fa-shoe-prints",
        onSale: true
    },
    
    // Tennis Products
    {
        id: 13,
        name: "Tennis Racket Carbon",
        category: "tennis",
        price: 199.99,
        description: "Professional carbon fiber tennis racket",
        icon: "fas fa-table-tennis",
        onSale: false
    },
    {
        id: 14,
        name: "Tennis Balls (4 pack)",
        category: "tennis",
        price: 8.99,
        description: "High-quality tennis balls for all court surfaces",
        icon: "fas fa-tennis-ball",
        onSale: false
    },
    {
        id: 15,
        name: "Tennis Shoes Court",
        category: "tennis",
        price: 119.99,
        originalPrice: 149.99,
        description: "Specialized tennis shoes for court play",
        icon: "fas fa-shoe-prints",
        onSale: true
    }
];

// Shopping Cart
let cart = JSON.parse(localStorage.getItem('sportzone-cart')) || [];

// DOM Elements
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');
const cartModal = document.getElementById('cartModal');
const cartCount = document.querySelector('.cart-count');
const cartItems = document.getElementById('cartItems');
const cartTotal = document.getElementById('cartTotal');
const productsGrid = document.getElementById('productsGrid');
const hotSaleProducts = document.getElementById('hotSaleProducts');
const categoryFilter = document.getElementById('categoryFilter');
const sortFilter = document.getElementById('sortFilter');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    setupEventListeners();
    displayProducts(sportsProducts);
    displayHotSaleProducts();
    updateCartUI();
    setupSmoothScrolling();
}

function setupEventListeners() {
    // Mobile menu toggle
    hamburger.addEventListener('click', toggleMobileMenu);
    
    // Cart modal
    document.querySelector('.cart-link').addEventListener('click', openCartModal);
    document.querySelector('.close').addEventListener('click', closeCartModal);
    document.getElementById('clearCart').addEventListener('click', clearCart);
    document.getElementById('checkout').addEventListener('click', checkout);
    
    // Category filtering
    categoryFilter.addEventListener('change', filterProducts);
    sortFilter.addEventListener('change', filterProducts);
    
    // Category cards
    document.querySelectorAll('.category-card').forEach(card => {
        card.addEventListener('click', function() {
            const category = this.dataset.category;
            if (category === 'all') {
                categoryFilter.value = 'all';
            } else {
                categoryFilter.value = category;
            }
            filterProducts();
            document.getElementById('products').scrollIntoView({ behavior: 'smooth' });
        });
    });
    
    // Dropdown menu items
    document.querySelectorAll('.dropdown-content a').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const category = this.dataset.category;
            categoryFilter.value = category;
            filterProducts();
            document.getElementById('products').scrollIntoView({ behavior: 'smooth' });
        });
    });
    
    // CTA button
    document.querySelector('.cta-button').addEventListener('click', function() {
        document.getElementById('categories').scrollIntoView({ behavior: 'smooth' });
    });
    
    // Close modal when clicking outside
    window.addEventListener('click', function(e) {
        if (e.target === cartModal) {
            closeCartModal();
        }
    });
}

function toggleMobileMenu() {
    hamburger.classList.toggle('active');
    navMenu.classList.toggle('active');
}

function displayProducts(products) {
    productsGrid.innerHTML = '';
    
    products.forEach(product => {
        const productCard = createProductCard(product);
        productsGrid.appendChild(productCard);
    });
}

function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card';
    
    card.innerHTML = `
        <div class="product-image">
            <i class="${product.icon}"></i>
            ${product.onSale ? '<div class="sale-badge">SALE</div>' : ''}
        </div>
        <div class="product-info">
            <div class="product-category">${product.category}</div>
            <div class="product-name">${product.name}</div>
            <div class="product-description">${product.description}</div>
            <div class="product-price">
                <span class="price">$${product.price}</span>
                ${product.originalPrice ? `<span class="original-price">$${product.originalPrice}</span>` : ''}
            </div>
            <button class="add-to-cart" onclick="addToCart(${product.id})">
                <i class="fas fa-cart-plus"></i> Add to Cart
            </button>
        </div>
    `;
    
    return card;
}

function displayHotSaleProducts() {
    const saleProducts = sportsProducts.filter(product => product.onSale).slice(0, 6);
    hotSaleProducts.innerHTML = '';
    
    saleProducts.forEach(product => {
        const productCard = createProductCard(product);
        hotSaleProducts.appendChild(productCard);
    });
}

function filterProducts() {
    const categoryValue = categoryFilter.value;
    const sortValue = sortFilter.value;
    
    let filteredProducts = sportsProducts;
    
    // Filter by category
    if (categoryValue !== 'all') {
        filteredProducts = filteredProducts.filter(product => product.category === categoryValue);
    }
    
    // Sort products
    switch (sortValue) {
        case 'price-low':
            filteredProducts.sort((a, b) => a.price - b.price);
            break;
        case 'price-high':
            filteredProducts.sort((a, b) => b.price - a.price);
            break;
        case 'name':
        default:
            filteredProducts.sort((a, b) => a.name.localeCompare(b.name));
            break;
    }
    
    displayProducts(filteredProducts);
}

function addToCart(productId) {
    const product = sportsProducts.find(p => p.id === productId);
    const existingItem = cart.find(item => item.id === productId);
    
    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            ...product,
            quantity: 1
        });
    }
    
    updateCartUI();
    saveCartToStorage();
    showAddToCartFeedback();
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    updateCartUI();
    saveCartToStorage();
}

function updateQuantity(productId, change) {
    const item = cart.find(item => item.id === productId);
    if (item) {
        item.quantity += change;
        if (item.quantity <= 0) {
            removeFromCart(productId);
        } else {
            updateCartUI();
            saveCartToStorage();
        }
    }
}

function updateCartUI() {
    // Update cart count
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    cartCount.textContent = totalItems;
    
    // Update cart items display
    if (cart.length === 0) {
        cartItems.innerHTML = `
            <div class="empty-cart">
                <i class="fas fa-shopping-cart"></i>
                <p>Your cart is empty</p>
            </div>
        `;
    } else {
        cartItems.innerHTML = cart.map(item => `
            <div class="cart-item">
                <div class="cart-item-image">
                    <i class="${item.icon}"></i>
                </div>
                <div class="cart-item-info">
                    <div class="cart-item-name">${item.name}</div>
                    <div class="cart-item-price">$${item.price}</div>
                </div>
                <div class="cart-item-controls">
                    <button class="quantity-btn" onclick="updateQuantity(${item.id}, -1)">-</button>
                    <span class="quantity">${item.quantity}</span>
                    <button class="quantity-btn" onclick="updateQuantity(${item.id}, 1)">+</button>
                    <button class="remove-item" onclick="removeFromCart(${item.id})">Remove</button>
                </div>
            </div>
        `).join('');
    }
    
    // Update total
    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    cartTotal.textContent = total.toFixed(2);
}

function openCartModal() {
    cartModal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeCartModal() {
    cartModal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

function clearCart() {
    cart = [];
    updateCartUI();
    saveCartToStorage();
}

function checkout() {
    if (cart.length === 0) {
        alert('Your cart is empty!');
        return;
    }
    
    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    alert(`Thank you for your purchase! Total: $${total.toFixed(2)}\n\nThis is a demo. In a real store, you would be redirected to payment processing.`);
    
    clearCart();
    closeCartModal();
}

function saveCartToStorage() {
    localStorage.setItem('sportzone-cart', JSON.stringify(cart));
}

function showAddToCartFeedback() {
    // Create a temporary notification
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 3000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    notification.innerHTML = '<i class="fas fa-check"></i> Added to cart!';
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

function setupSmoothScrolling() {
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}
