<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - SportZone</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <i class="fas fa-running"></i>
                <span>SportZone</span>
            </div>
            <button class="sidebar-close" id="sidebarClose">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="sidebar-content">
            <div class="sidebar-section">
                <h3>Browse</h3>
                <ul class="sidebar-menu">
                    <li><a href="index.html" class="sidebar-link"><i class="fas fa-home"></i> Home</a></li>
                    <li><a href="index.html#popular" class="sidebar-link"><i class="fas fa-fire"></i> Most Popular</a></li>
                    <li><a href="index.html#hotsale" class="sidebar-link"><i class="fas fa-percent"></i> Hot Sales</a></li>
                    <li><a href="index.html#new" class="sidebar-link"><i class="fas fa-star"></i> New Arrivals</a></li>
                </ul>
            </div>
            
            <div class="sidebar-section">
                <h3>Sports Categories</h3>
                <ul class="sidebar-menu">
                    <li><a href="index.html" class="sidebar-link" data-category="all"><i class="fas fa-th-large"></i> All Sports</a></li>
                    <li><a href="index.html" class="sidebar-link" data-category="soccer"><i class="fas fa-futbol"></i> Soccer</a></li>
                    <li><a href="index.html" class="sidebar-link" data-category="badminton"><i class="fas fa-table-tennis"></i> Badminton</a></li>
                    <li><a href="index.html" class="sidebar-link" data-category="pingpong"><i class="fas fa-ping-pong-paddle-ball"></i> Ping Pong</a></li>
                    <li><a href="index.html" class="sidebar-link" data-category="basketball"><i class="fas fa-basketball-ball"></i> Basketball</a></li>
                    <li><a href="index.html" class="sidebar-link" data-category="tennis"><i class="fas fa-tennis-ball"></i> Tennis</a></li>
                </ul>
            </div>
            
            <div class="sidebar-section">
                <h3>Account</h3>
                <ul class="sidebar-menu">
                    <li><a href="profile.html" class="sidebar-link active"><i class="fas fa-user"></i> Profile</a></li>
                    <li><a href="cart.html" class="sidebar-link"><i class="fas fa-shopping-cart"></i> Cart <span class="cart-count-sidebar">0</span></a></li>
                    <li><a href="signin.html" class="sidebar-link"><i class="fas fa-sign-in-alt"></i> Sign In</a></li>
                    <li><a href="signup.html" class="sidebar-link"><i class="fas fa-user-plus"></i> Sign Up</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <button class="sidebar-toggle" id="sidebarToggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </button>
            <div class="nav-logo">
                <i class="fas fa-running"></i>
                <span>SportZone</span>
            </div>
            <div class="nav-actions">
                <a href="signin.html" class="nav-signin">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>Sign In</span>
                </a>
                <a href="cart.html" class="nav-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </a>
                <a href="profile.html" class="nav-profile active">
                    <i class="fas fa-user"></i>
                </a>
            </div>
        </div>
    </nav>

    <!-- Profile Page Content -->
    <main class="profile-page">
        <div class="container">
            <div class="page-header">
                <h1><i class="fas fa-user"></i> My Profile</h1>
                <p>Manage your account and view your purchase history</p>
            </div>

            <div class="profile-content">
                <div class="profile-sidebar">
                    <div class="profile-card">
                        <div class="profile-avatar">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <h3>John Doe</h3>
                        <p class="profile-email"><EMAIL></p>
                        <p class="member-since">Member since: Jan 2024</p>
                        
                        <div class="profile-stats">
                            <div class="stat">
                                <span class="stat-number">12</span>
                                <span class="stat-label">Orders</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">$1,247</span>
                                <span class="stat-label">Total Spent</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">Gold</span>
                                <span class="stat-label">Status</span>
                            </div>
                        </div>
                        
                        <button class="edit-profile-btn">
                            <i class="fas fa-edit"></i> Edit Profile
                        </button>
                    </div>
                </div>

                <div class="profile-main">
                    <div class="profile-tabs">
                        <button class="tab-btn active" data-tab="orders">
                            <i class="fas fa-box"></i> Order History
                        </button>
                        <button class="tab-btn" data-tab="details">
                            <i class="fas fa-info-circle"></i> Account Details
                        </button>
                        <button class="tab-btn" data-tab="addresses">
                            <i class="fas fa-map-marker-alt"></i> Addresses
                        </button>
                        <button class="tab-btn" data-tab="settings">
                            <i class="fas fa-cog"></i> Settings
                        </button>
                    </div>

                    <div class="tab-content">
                        <!-- Orders Tab -->
                        <div class="tab-pane active" id="orders">
                            <h3>Recent Orders</h3>
                            <div class="orders-list">
                                <div class="order-item">
                                    <div class="order-header">
                                        <div class="order-info">
                                            <span class="order-number">#ORD-2024-001</span>
                                            <span class="order-date">March 15, 2024</span>
                                        </div>
                                        <div class="order-status delivered">Delivered</div>
                                    </div>
                                    <div class="order-items">
                                        <div class="order-product">
                                            <i class="fas fa-tshirt"></i>
                                            <span>Professional Soccer Jersey</span>
                                            <span class="product-price">$89.99</span>
                                        </div>
                                        <div class="order-product">
                                            <i class="fas fa-shoe-prints"></i>
                                            <span>Soccer Cleats Elite</span>
                                            <span class="product-price">$129.99</span>
                                        </div>
                                    </div>
                                    <div class="order-total">Total: $219.98</div>
                                </div>

                                <div class="order-item">
                                    <div class="order-header">
                                        <div class="order-info">
                                            <span class="order-number">#ORD-2024-002</span>
                                            <span class="order-date">March 10, 2024</span>
                                        </div>
                                        <div class="order-status shipped">Shipped</div>
                                    </div>
                                    <div class="order-items">
                                        <div class="order-product">
                                            <i class="fas fa-table-tennis"></i>
                                            <span>Carbon Fiber Badminton Racket</span>
                                            <span class="product-price">$79.99</span>
                                        </div>
                                    </div>
                                    <div class="order-total">Total: $79.99</div>
                                </div>

                                <div class="order-item">
                                    <div class="order-header">
                                        <div class="order-info">
                                            <span class="order-number">#ORD-2024-003</span>
                                            <span class="order-date">March 5, 2024</span>
                                        </div>
                                        <div class="order-status processing">Processing</div>
                                    </div>
                                    <div class="order-items">
                                        <div class="order-product">
                                            <i class="fas fa-basketball-ball"></i>
                                            <span>Basketball Pro</span>
                                            <span class="product-price">$39.99</span>
                                        </div>
                                        <div class="order-product">
                                            <i class="fas fa-shoe-prints"></i>
                                            <span>Basketball Shoes Elite</span>
                                            <span class="product-price">$149.99</span>
                                        </div>
                                    </div>
                                    <div class="order-total">Total: $189.98</div>
                                </div>
                            </div>
                        </div>

                        <!-- Account Details Tab -->
                        <div class="tab-pane" id="details">
                            <h3>Account Information</h3>
                            <div class="details-form">
                                <div class="form-group">
                                    <label>Full Name</label>
                                    <input type="text" value="John Doe" readonly>
                                </div>
                                <div class="form-group">
                                    <label>Email</label>
                                    <input type="email" value="<EMAIL>" readonly>
                                </div>
                                <div class="form-group">
                                    <label>Phone</label>
                                    <input type="tel" value="+1 (*************" readonly>
                                </div>
                                <div class="form-group">
                                    <label>Date of Birth</label>
                                    <input type="date" value="1990-01-15" readonly>
                                </div>
                                <button class="edit-details-btn">Edit Details</button>
                            </div>
                        </div>

                        <!-- Addresses Tab -->
                        <div class="tab-pane" id="addresses">
                            <h3>Saved Addresses</h3>
                            <div class="addresses-list">
                                <div class="address-item">
                                    <div class="address-header">
                                        <h4>Home Address</h4>
                                        <span class="address-default">Default</span>
                                    </div>
                                    <p>123 Main Street<br>
                                    Apartment 4B<br>
                                    New York, NY 10001<br>
                                    United States</p>
                                    <div class="address-actions">
                                        <button class="edit-address-btn">Edit</button>
                                        <button class="delete-address-btn">Delete</button>
                                    </div>
                                </div>
                                <button class="add-address-btn">
                                    <i class="fas fa-plus"></i> Add New Address
                                </button>
                            </div>
                        </div>

                        <!-- Settings Tab -->
                        <div class="tab-pane" id="settings">
                            <h3>Account Settings</h3>
                            <div class="settings-list">
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Email Notifications</h4>
                                        <p>Receive updates about your orders and promotions</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>SMS Notifications</h4>
                                        <p>Get text messages about order updates</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Marketing Emails</h4>
                                        <p>Receive promotional offers and deals</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Two-Factor Authentication</h4>
                                        <p>Add an extra layer of security to your account</p>
                                    </div>
                                    <button class="setup-2fa-btn">Setup</button>
                                </div>
                                <div class="setting-item danger">
                                    <div class="setting-info">
                                        <h4>Delete Account</h4>
                                        <p>Permanently delete your account and all data</p>
                                    </div>
                                    <button class="delete-account-btn">Delete</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>SportZone</h3>
                    <p>Your trusted partner for all sports equipment and gear.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="cart.html">Cart</a></li>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="signin.html">Sign In</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Email: <EMAIL></p>
                    <p>Phone: (*************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 SportZone. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script src="profile.js"></script>
</body>
</html>
