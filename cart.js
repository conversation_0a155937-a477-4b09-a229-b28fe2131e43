// Cart Page Specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeCartPage();
});

function initializeCartPage() {
    updateCartPageUI();
    setupCartPageEventListeners();
}

function setupCartPageEventListeners() {
    const clearAllBtn = document.getElementById('clearAllCart');
    const checkoutBtn = document.getElementById('checkoutBtn');
    const promoApplyBtn = document.querySelector('.apply-promo-btn');
    
    if (clearAllBtn) {
        clearAllBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to clear all items from your cart?')) {
                clearCart();
                updateCartPageUI();
            }
        });
    }
    
    if (checkoutBtn) {
        checkoutBtn.addEventListener('click', function() {
            if (cart.length === 0) {
                alert('Your cart is empty!');
                return;
            }
            
            const total = calculateTotal();
            alert(`Thank you for your purchase! Total: $${total.toFixed(2)}\n\nThis is a demo. In a real store, you would be redirected to payment processing.`);
            
            clearCart();
            updateCartPageUI();
        });
    }
    
    if (promoApplyBtn) {
        promoApplyBtn.addEventListener('click', function() {
            const promoCode = document.getElementById('promoCode').value.trim();
            if (promoCode) {
                applyPromoCode(promoCode);
            }
        });
    }
}

function updateCartPageUI() {
    const cartItemsList = document.getElementById('cartItemsList');
    const emptyCartState = document.getElementById('emptyCartState');
    const cartPage = document.querySelector('.cart-page');
    
    if (cart.length === 0) {
        if (cartPage) cartPage.style.display = 'none';
        if (emptyCartState) emptyCartState.style.display = 'block';
        return;
    }
    
    if (cartPage) cartPage.style.display = 'block';
    if (emptyCartState) emptyCartState.style.display = 'none';
    
    if (cartItemsList) {
        cartItemsList.innerHTML = cart.map(item => `
            <div class="cart-item">
                <div class="cart-item-image">
                    <i class="${item.icon}"></i>
                </div>
                <div class="cart-item-info">
                    <div class="cart-item-name">${item.name}</div>
                    <div class="cart-item-category">${item.category}</div>
                    <div class="cart-item-price">$${item.price}</div>
                    <div class="cart-item-description">${item.description}</div>
                </div>
                <div class="cart-item-controls">
                    <button class="quantity-btn" onclick="updateQuantityCartPage(${item.id}, -1)">-</button>
                    <span class="quantity">${item.quantity}</span>
                    <button class="quantity-btn" onclick="updateQuantityCartPage(${item.id}, 1)">+</button>
                    <button class="remove-item" onclick="removeFromCartPage(${item.id})">Remove</button>
                </div>
            </div>
        `).join('');
    }
    
    updateCartSummary();
}

function updateCartSummary() {
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const shipping = subtotal > 50 ? 0 : 9.99;
    const tax = subtotal * 0.08; // 8% tax
    const total = subtotal + shipping + tax;
    
    const cartSubtotal = document.getElementById('cartSubtotal');
    const cartShipping = document.getElementById('cartShipping');
    const cartTax = document.getElementById('cartTax');
    const cartTotal = document.getElementById('cartTotal');
    
    if (cartSubtotal) cartSubtotal.textContent = `$${subtotal.toFixed(2)}`;
    if (cartShipping) cartShipping.textContent = shipping === 0 ? 'FREE' : `$${shipping.toFixed(2)}`;
    if (cartTax) cartTax.textContent = `$${tax.toFixed(2)}`;
    if (cartTotal) cartTotal.textContent = `$${total.toFixed(2)}`;
}

function calculateTotal() {
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const shipping = subtotal > 50 ? 0 : 9.99;
    const tax = subtotal * 0.08;
    return subtotal + shipping + tax;
}

function updateQuantityCartPage(productId, change) {
    const item = cart.find(item => item.id === productId);
    if (item) {
        item.quantity += change;
        if (item.quantity <= 0) {
            removeFromCartPage(productId);
        } else {
            updateCartPageUI();
            saveCartToStorage();
            updateCartUI(); // Update main cart UI
        }
    }
}

function removeFromCartPage(productId) {
    cart = cart.filter(item => item.id !== productId);
    updateCartPageUI();
    saveCartToStorage();
    updateCartUI(); // Update main cart UI
}

function applyPromoCode(code) {
    const validCodes = {
        'SAVE10': 0.10,
        'WELCOME': 0.15,
        'SPORTS20': 0.20
    };
    
    const discount = validCodes[code.toUpperCase()];
    if (discount) {
        alert(`Promo code applied! You saved ${(discount * 100)}%`);
        // In a real application, you would apply the discount to the total
        document.getElementById('promoCode').value = '';
    } else {
        alert('Invalid promo code. Please try again.');
    }
}

// Enhanced cart item styling for cart page
const cartPageStyles = `
    .cart-item {
        display: flex;
        align-items: center;
        padding: 1.5rem;
        border: 1px solid #eee;
        border-radius: 10px;
        margin-bottom: 1rem;
        gap: 1.5rem;
        transition: box-shadow 0.3s ease;
    }
    
    .cart-item:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .cart-item-image {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        flex-shrink: 0;
    }
    
    .cart-item-info {
        flex: 1;
    }
    
    .cart-item-name {
        font-weight: 600;
        font-size: 1.1rem;
        color: #333;
        margin-bottom: 0.25rem;
    }
    
    .cart-item-category {
        color: #667eea;
        font-size: 0.9rem;
        text-transform: uppercase;
        font-weight: 500;
        margin-bottom: 0.25rem;
    }
    
    .cart-item-price {
        color: #333;
        font-weight: 600;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }
    
    .cart-item-description {
        color: #666;
        font-size: 0.9rem;
        line-height: 1.4;
    }
    
    .cart-item-controls {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex-shrink: 0;
    }
    
    @media (max-width: 768px) {
        .cart-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }
        
        .cart-item-controls {
            align-self: flex-end;
        }
    }
`;

// Inject cart page specific styles
if (!document.getElementById('cart-page-styles')) {
    const styleSheet = document.createElement('style');
    styleSheet.id = 'cart-page-styles';
    styleSheet.textContent = cartPageStyles;
    document.head.appendChild(styleSheet);
}
