// Cart Page Specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeCartPage();
});

function initializeCartPage() {
    updateCartPageUI();
    setupCartPageEventListeners();
}

function setupCartPageEventListeners() {
    const clearAllBtn = document.getElementById('clearAllCart');
    const checkoutBtn = document.getElementById('checkoutBtn');
    const promoApplyBtn = document.querySelector('.apply-promo-btn');
    
    if (clearAllBtn) {
        clearAllBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to clear all items from your cart?')) {
                clearCart();
                updateCartPageUI();
            }
        });
    }
    
    if (checkoutBtn) {
        checkoutBtn.addEventListener('click', function() {
            if (cart.length === 0) {
                alert('Your cart is empty!');
                return;
            }
            
            const total = calculateTotal();
            alert(`Thank you for your purchase! Total: $${total.toFixed(2)}\n\nThis is a demo. In a real store, you would be redirected to payment processing.`);
            
            clearCart();
            updateCartPageUI();
        });
    }
    
    if (promoApplyBtn) {
        promoApplyBtn.addEventListener('click', function() {
            const promoCode = document.getElementById('promoCode').value.trim();
            if (promoCode) {
                applyPromoCode(promoCode);
            }
        });
    }
}

function updateCartPageUI() {
    const cartItemsList = document.getElementById('cartItemsList');
    const emptyCartState = document.getElementById('emptyCartState');
    const cartModalContent = document.querySelector('.cart-modal-content');

    if (cart.length === 0) {
        if (cartModalContent) cartModalContent.style.display = 'none';
        if (emptyCartState) emptyCartState.style.display = 'block';
        return;
    }

    if (cartModalContent) cartModalContent.style.display = 'block';
    if (emptyCartState) emptyCartState.style.display = 'none';

    if (cartItemsList) {
        cartItemsList.innerHTML = cart.map(item => `
            <div class="cart-item-modal">
                <div class="cart-item-image">
                    <i class="${item.icon}"></i>
                </div>
                <div class="cart-item-info">
                    <div class="cart-item-name">${item.name}</div>
                    <div class="cart-item-category">${item.category}</div>
                    <div class="cart-item-price">$${item.price}</div>
                </div>
                <div class="cart-item-controls">
                    <button class="quantity-btn" onclick="updateQuantityCartPage(${item.id}, -1)">-</button>
                    <span class="quantity">${item.quantity}</span>
                    <button class="quantity-btn" onclick="updateQuantityCartPage(${item.id}, 1)">+</button>
                    <button class="remove-item" onclick="removeFromCartPage(${item.id})">Remove</button>
                </div>
            </div>
        `).join('');
    }

    updateCartSummary();
}

function updateCartSummary() {
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const shipping = subtotal > 50 ? 0 : 9.99;
    const tax = subtotal * 0.08; // 8% tax
    const total = subtotal + shipping + tax;
    
    const cartSubtotal = document.getElementById('cartSubtotal');
    const cartShipping = document.getElementById('cartShipping');
    const cartTax = document.getElementById('cartTax');
    const cartTotal = document.getElementById('cartTotal');
    
    if (cartSubtotal) cartSubtotal.textContent = `$${subtotal.toFixed(2)}`;
    if (cartShipping) cartShipping.textContent = shipping === 0 ? 'FREE' : `$${shipping.toFixed(2)}`;
    if (cartTax) cartTax.textContent = `$${tax.toFixed(2)}`;
    if (cartTotal) cartTotal.textContent = `$${total.toFixed(2)}`;
}

function calculateTotal() {
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const shipping = subtotal > 50 ? 0 : 9.99;
    const tax = subtotal * 0.08;
    return subtotal + shipping + tax;
}

function updateQuantityCartPage(productId, change) {
    const item = cart.find(item => item.id === productId);
    if (item) {
        item.quantity += change;
        if (item.quantity <= 0) {
            removeFromCartPage(productId);
        } else {
            updateCartPageUI();
            saveCartToStorage();
            updateCartUI(); // Update main cart UI
        }
    }
}

function removeFromCartPage(productId) {
    cart = cart.filter(item => item.id !== productId);
    updateCartPageUI();
    saveCartToStorage();
    updateCartUI(); // Update main cart UI
}

function applyPromoCode(code) {
    const validCodes = {
        'SAVE10': 0.10,
        'WELCOME': 0.15,
        'SPORTS20': 0.20
    };
    
    const discount = validCodes[code.toUpperCase()];
    if (discount) {
        alert(`Promo code applied! You saved ${(discount * 100)}%`);
        // In a real application, you would apply the discount to the total
        document.getElementById('promoCode').value = '';
    } else {
        alert('Invalid promo code. Please try again.');
    }
}

// Enhanced cart item styling for cart page - Modal Style
const cartPageStyles = `
    .cart-item-modal {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-bottom: 1px solid #eee;
        gap: 1rem;
        transition: background-color 0.3s ease;
    }

    .cart-item-modal:last-child {
        border-bottom: none;
    }

    .cart-item-modal:hover {
        background: rgba(102, 126, 234, 0.05);
    }

    .cart-item-modal .cart-item-image {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        flex-shrink: 0;
    }

    .cart-item-modal .cart-item-info {
        flex: 1;
    }

    .cart-item-modal .cart-item-name {
        font-weight: 600;
        color: #333;
        margin-bottom: 0.25rem;
    }

    .cart-item-modal .cart-item-category {
        color: #667eea;
        font-size: 0.8rem;
        text-transform: uppercase;
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .cart-item-modal .cart-item-price {
        color: #667eea;
        font-weight: 500;
    }

    .cart-item-modal .cart-item-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex-shrink: 0;
    }

    .cart-item-modal .quantity-btn {
        background: #667eea;
        color: white;
        border: none;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.3s ease;
    }

    .cart-item-modal .quantity-btn:hover {
        background: #5a6fd8;
    }

    .cart-item-modal .quantity {
        font-weight: 600;
        min-width: 30px;
        text-align: center;
    }

    .cart-item-modal .remove-item {
        background: #ff4757;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 0.8rem;
        transition: background-color 0.3s ease;
    }

    .cart-item-modal .remove-item:hover {
        background: #ff3838;
    }

    @media (max-width: 768px) {
        .cart-item-modal {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
            padding: 1.5rem 1rem;
        }

        .cart-item-modal .cart-item-controls {
            align-self: flex-end;
        }
    }
`;

// Inject cart page specific styles
if (!document.getElementById('cart-page-styles')) {
    const styleSheet = document.createElement('style');
    styleSheet.id = 'cart-page-styles';
    styleSheet.textContent = cartPageStyles;
    document.head.appendChild(styleSheet);
}
