<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SportZone - Your Ultimate Sports Store</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <i class="fas fa-running"></i>
                <span>SportZone</span>
            </div>
            <button class="sidebar-close" id="sidebarClose">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="sidebar-content">
            <div class="sidebar-section">
                <h3>Browse</h3>
                <ul class="sidebar-menu">
                    <li><a href="index.html" class="sidebar-link"><i class="fas fa-home"></i> Home</a></li>
                    <li><a href="#" class="sidebar-link" data-filter="popular"><i class="fas fa-fire"></i> Most Popular</a></li>
                    <li><a href="#" class="sidebar-link" data-filter="hotsale"><i class="fas fa-percent"></i> Hot Sales</a></li>
                    <li><a href="#" class="sidebar-link" data-filter="new"><i class="fas fa-star"></i> New Arrivals</a></li>
                </ul>
            </div>

            <div class="sidebar-section">
                <h3>Sports Categories</h3>
                <ul class="sidebar-menu">
                    <li><a href="#" class="sidebar-link" data-category="all"><i class="fas fa-th-large"></i> All Sports</a></li>
                    <li><a href="#" class="sidebar-link" data-category="soccer"><i class="fas fa-futbol"></i> Soccer</a></li>
                    <li><a href="#" class="sidebar-link" data-category="badminton"><i class="fas fa-table-tennis"></i> Badminton</a></li>
                    <li><a href="#" class="sidebar-link" data-category="pingpong"><i class="fas fa-ping-pong-paddle-ball"></i> Ping Pong</a></li>
                    <li><a href="#" class="sidebar-link" data-category="basketball"><i class="fas fa-basketball-ball"></i> Basketball</a></li>
                    <li><a href="#" class="sidebar-link" data-category="tennis"><i class="fas fa-tennis-ball"></i> Tennis</a></li>
                </ul>
            </div>

            <div class="sidebar-section">
                <h3>Account</h3>
                <ul class="sidebar-menu">
                    <li><a href="profile.html" class="sidebar-link"><i class="fas fa-user"></i> Profile</a></li>
                    <li><a href="cart.html" class="sidebar-link"><i class="fas fa-shopping-cart"></i> Cart <span class="cart-count-sidebar">0</span></a></li>
                    <li><a href="signin.html" class="sidebar-link"><i class="fas fa-sign-in-alt"></i> Sign In</a></li>
                    <li><a href="signup.html" class="sidebar-link"><i class="fas fa-user-plus"></i> Sign Up</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <button class="sidebar-toggle" id="sidebarToggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </button>
            <div class="nav-logo">
                <i class="fas fa-running"></i>
                <span>SportZone</span>
            </div>
            <div class="nav-actions">
                <a href="cart.html" class="nav-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </a>
                <a href="profile.html" class="nav-profile">
                    <i class="fas fa-user"></i>
                </a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content">
            <h1>Welcome to SportZone</h1>
            <p>Your one-stop destination for all sports equipment and gear</p>
            <button class="cta-button">Shop Now</button>
        </div>
        <div class="hero-image">
            <i class="fas fa-trophy"></i>
        </div>
    </section>

    <!-- Categories Section -->
    <section id="categories" class="categories">
        <div class="container">
            <h2>Shop by Category</h2>
            <div class="category-grid">
                <div class="category-card" data-category="soccer">
                    <i class="fas fa-futbol"></i>
                    <h3>Soccer</h3>
                    <p>Jerseys, Gloves, Shoes</p>
                </div>
                <div class="category-card" data-category="badminton">
                    <i class="fas fa-table-tennis"></i>
                    <h3>Badminton</h3>
                    <p>Rackets, Grips, Shuttles</p>
                </div>
                <div class="category-card" data-category="pingpong">
                    <i class="fas fa-ping-pong-paddle-ball"></i>
                    <h3>Ping Pong</h3>
                    <p>Bats, Balls, Tables, Nets</p>
                </div>
                <div class="category-card" data-category="basketball">
                    <i class="fas fa-basketball-ball"></i>
                    <h3>Basketball</h3>
                    <p>Balls, Shoes, Jerseys</p>
                </div>
                <div class="category-card" data-category="tennis">
                    <i class="fas fa-tennis-ball"></i>
                    <h3>Tennis</h3>
                    <p>Rackets, Balls, Shoes</p>
                </div>
                <div class="category-card" data-category="all">
                    <i class="fas fa-th-large"></i>
                    <h3>All Sports</h3>
                    <p>Browse Everything</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Hot Sale Section -->
    <section id="hotsale" class="hot-sale">
        <div class="container">
            <h2>🔥 Hot Sale Items</h2>
            <div class="hot-sale-grid" id="hotSaleProducts">
                <!-- Hot sale products will be populated by JavaScript -->
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section id="products" class="products">
        <div class="container">
            <div class="products-header">
                <h2>Our Products</h2>
                <div class="filter-controls">
                    <select id="categoryFilter">
                        <option value="all">All Categories</option>
                        <option value="soccer">Soccer</option>
                        <option value="badminton">Badminton</option>
                        <option value="pingpong">Ping Pong</option>
                        <option value="basketball">Basketball</option>
                        <option value="tennis">Tennis</option>
                    </select>
                    <select id="sortFilter">
                        <option value="name">Sort by Name</option>
                        <option value="price-low">Price: Low to High</option>
                        <option value="price-high">Price: High to Low</option>
                    </select>
                </div>
            </div>
            <div class="products-grid" id="productsGrid">
                <!-- Products will be populated by JavaScript -->
            </div>
        </div>
    </section>

    <!-- Cart Modal -->
    <div id="cartModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Shopping Cart</h2>
                <span class="close">&times;</span>
            </div>
            <div class="cart-items" id="cartItems">
                <!-- Cart items will be populated by JavaScript -->
            </div>
            <div class="cart-footer">
                <div class="cart-total">
                    <strong>Total: $<span id="cartTotal">0.00</span></strong>
                </div>
                <div class="cart-actions">
                    <button class="btn-secondary" id="clearCart">Clear Cart</button>
                    <button class="btn-primary" id="checkout">Checkout</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>SportZone</h3>
                    <p>Your trusted partner for all sports equipment and gear.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#home">Home</a></li>
                        <li><a href="#categories">Categories</a></li>
                        <li><a href="#hotsale">Hot Sale</a></li>
                        <li><a href="#profile">Profile</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Email: <EMAIL></p>
                    <p>Phone: (*************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 SportZone. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
