/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    left: -350px;
    width: 350px;
    height: 100vh;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    z-index: 2000;
    transition: left 0.3s ease;
    overflow-y: auto;
}

.sidebar.active {
    left: 0;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #34495e;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffd700;
}

.sidebar-logo i {
    margin-right: 0.5rem;
    font-size: 1.8rem;
}

.sidebar-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.sidebar-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar-content {
    padding: 1rem 0;
}

.sidebar-section {
    margin-bottom: 2rem;
}

.sidebar-section h3 {
    color: #ffd700;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 0 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.sidebar-menu {
    list-style: none;
}

.sidebar-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.sidebar-link:hover, .sidebar-link.active {
    background: rgba(255, 215, 0, 0.1);
    color: #ffd700;
    border-left-color: #ffd700;
}

.sidebar-link i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

.cart-count-sidebar {
    background: #ff4757;
    color: white;
    border-radius: 50%;
    padding: 0.2rem 0.5rem;
    font-size: 0.7rem;
    margin-left: auto;
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1500;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Navigation Styles */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-toggle {
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    padding: 0.5rem;
}

.sidebar-toggle .bar {
    width: 25px;
    height: 3px;
    background: white;
    margin: 2px 0;
    transition: 0.3s;
    border-radius: 2px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
}

.nav-logo i {
    margin-right: 0.5rem;
    font-size: 1.8rem;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-cart, .nav-profile {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-cart:hover, .nav-profile:hover, .nav-cart.active, .nav-profile.active {
    background: rgba(255, 255, 255, 0.1);
    color: #ffd700;
}

.cart-count {
    background: #ff4757;
    color: white;
    border-radius: 50%;
    padding: 0.2rem 0.5rem;
    font-size: 0.8rem;
    margin-left: 0.5rem;
    position: absolute;
    top: -5px;
    right: -5px;
    min-width: 20px;
    text-align: center;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 80px;
    display: flex;
    align-items: center;
    min-height: 70vh;
}

.hero-content {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-button {
    background: #ffd700;
    color: #333;
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(255, 215, 0, 0.3);
}

.hero-image {
    flex: 1;
    text-align: center;
}

.hero-image i {
    font-size: 15rem;
    opacity: 0.3;
}

/* Categories Section */
.categories {
    padding: 80px 0;
    background: white;
}

.categories h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.category-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.category-card i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.category-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.category-card p {
    color: #666;
    font-size: 0.9rem;
}

/* Hot Sale Section */
.hot-sale {
    padding: 80px 0;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.hot-sale h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
}

.hot-sale-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

/* Products Section */
.products {
    padding: 80px 0;
    background: white;
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.products-header h2 {
    font-size: 2.5rem;
    color: #333;
}

.filter-controls {
    display: flex;
    gap: 1rem;
}

.filter-controls select {
    padding: 10px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.filter-controls select:focus {
    outline: none;
    border-color: #667eea;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

/* Product Card Styles */
.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.product-image {
    height: 200px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.product-image i {
    font-size: 4rem;
    color: white;
    opacity: 0.8;
}

.sale-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ff4757;
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.product-info {
    padding: 1.5rem;
}

.product-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.product-category {
    color: #667eea;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    font-weight: 500;
}

.product-description {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.product-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.price {
    font-size: 1.3rem;
    font-weight: 700;
    color: #333;
}

.original-price {
    text-decoration: line-through;
    color: #999;
    font-size: 1rem;
    margin-left: 0.5rem;
}

.add-to-cart {
    width: 100%;
    background: #667eea;
    color: white;
    border: none;
    padding: 12px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.add-to-cart:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.add-to-cart:active {
    transform: translateY(0);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.close {
    color: white;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.close:hover {
    opacity: 0.7;
}

.cart-items {
    max-height: 400px;
    overflow-y: auto;
    padding: 1rem;
}

.cart-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #eee;
    gap: 1rem;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-image {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.cart-item-price {
    color: #667eea;
    font-weight: 500;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-btn {
    background: #667eea;
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

.quantity-btn:hover {
    background: #5a6fd8;
}

.quantity {
    font-weight: 600;
    min-width: 30px;
    text-align: center;
}

.remove-item {
    background: #ff4757;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: background-color 0.3s ease;
}

.remove-item:hover {
    background: #ff3838;
}

.cart-footer {
    padding: 1.5rem;
    border-top: 1px solid #eee;
    background: #f8f9fa;
}

.cart-total {
    text-align: center;
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

.cart-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.btn-primary, .btn-secondary {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #e1e8ed;
    color: #333;
}

.btn-secondary:hover {
    background: #d1d9e0;
}

.empty-cart {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.empty-cart i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Footer Styles */
.footer {
    background: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3, .footer-section h4 {
    margin-bottom: 1rem;
    color: #ffd700;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #ffd700;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-item {
        margin: 1rem 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-content {
        flex-direction: column;
        text-align: center;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-image {
        margin-top: 2rem;
    }

    .hero-image i {
        font-size: 8rem;
    }

    .products-header {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-controls {
        flex-direction: column;
    }

    .category-grid, .products-grid, .hot-sale-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .cart-actions {
        flex-direction: column;
    }
}

/* Cart Page Styles */
.cart-page {
    padding: 120px 0 80px;
    min-height: 100vh;
    background: #f8f9fa;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-header h1 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.page-header p {
    color: #666;
    font-size: 1.1rem;
}

.cart-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: start;
}

.cart-items-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.cart-items-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.cart-items-header h2 {
    color: #333;
    font-size: 1.5rem;
}

.clear-all-btn {
    background: #ff4757;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.clear-all-btn:hover {
    background: #ff3838;
}

.cart-items-list {
    max-height: 500px;
    overflow-y: auto;
}

.cart-summary {
    position: sticky;
    top: 120px;
}

.summary-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
}

.summary-card h3 {
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    color: #666;
}

.summary-row.total {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    padding-top: 1rem;
}

.summary-divider {
    height: 1px;
    background: #eee;
    margin: 1rem 0;
}

.cart-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1.5rem;
}

.continue-shopping-btn, .checkout-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.continue-shopping-btn {
    background: #e1e8ed;
    color: #333;
}

.continue-shopping-btn:hover {
    background: #d1d9e0;
}

.checkout-btn {
    background: #667eea;
    color: white;
}

.checkout-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.promo-section {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.promo-section h4 {
    color: #333;
    margin-bottom: 1rem;
}

.promo-input {
    display: flex;
    gap: 0.5rem;
}

.promo-input input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 1rem;
}

.promo-input input:focus {
    outline: none;
    border-color: #667eea;
}

.apply-promo-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

.apply-promo-btn:hover {
    background: #5a6fd8;
}

.empty-cart-state {
    padding: 120px 0 80px;
    min-height: 100vh;
    background: #f8f9fa;
    display: flex;
    align-items: center;
}

.empty-cart-content {
    text-align: center;
    background: white;
    padding: 4rem 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    max-width: 500px;
    margin: 0 auto;
}

.empty-cart-content i {
    font-size: 5rem;
    color: #bdc3c7;
    margin-bottom: 1.5rem;
}

.empty-cart-content h2 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 2rem;
}

.empty-cart-content p {
    color: #666;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.start-shopping-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.start-shopping-btn:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .categories, .products, .hot-sale {
        padding: 60px 0;
    }

    .categories h2, .products h2, .hot-sale h2 {
        font-size: 2rem;
    }

    .cart-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .cart-item-controls {
        align-self: flex-end;
    }

    .cart-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .cart-summary {
        position: static;
    }
}

/* Profile Page Styles */
.profile-page {
    padding: 120px 0 80px;
    min-height: 100vh;
    background: #f8f9fa;
}

.profile-content {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 3rem;
    align-items: start;
}

.profile-sidebar {
    position: sticky;
    top: 120px;
}

.profile-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

.profile-avatar {
    margin-bottom: 1rem;
}

.profile-avatar i {
    font-size: 5rem;
    color: #667eea;
}

.profile-card h3 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.profile-email {
    color: #666;
    margin-bottom: 0.5rem;
}

.member-since {
    color: #999;
    font-size: 0.9rem;
    margin-bottom: 2rem;
}

.profile-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem 0;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    color: #667eea;
}

.stat-label {
    font-size: 0.8rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.edit-profile-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.3s ease;
    width: 100%;
}

.edit-profile-btn:hover {
    background: #5a6fd8;
}

.profile-main {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.profile-tabs {
    display: flex;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
}

.tab-btn {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover, .tab-btn.active {
    color: #667eea;
    background: white;
    border-bottom-color: #667eea;
}

.tab-content {
    padding: 2rem;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.tab-pane h3 {
    color: #333;
    margin-bottom: 2rem;
    font-size: 1.5rem;
}

/* Orders Styles */
.orders-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.order-item {
    border: 1px solid #eee;
    border-radius: 10px;
    padding: 1.5rem;
    transition: box-shadow 0.3s ease;
}

.order-item:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f0f0f0;
}

.order-number {
    font-weight: 600;
    color: #333;
}

.order-date {
    color: #666;
    font-size: 0.9rem;
}

.order-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.order-status.delivered {
    background: #d4edda;
    color: #155724;
}

.order-status.shipped {
    background: #cce7ff;
    color: #004085;
}

.order-status.processing {
    background: #fff3cd;
    color: #856404;
}

.order-items {
    margin-bottom: 1rem;
}

.order-product {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 0;
}

.order-product i {
    color: #667eea;
    width: 20px;
}

.order-product span:first-of-type {
    flex: 1;
    color: #333;
}

.product-price {
    font-weight: 600;
    color: #667eea;
}

.order-total {
    text-align: right;
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
}

/* Form Styles */
.details-form {
    display: grid;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input, .form-group select {
    padding: 0.75rem;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus, .form-group select:focus {
    outline: none;
    border-color: #667eea;
}

.form-group input[readonly] {
    background: #f8f9fa;
    color: #666;
}

.edit-details-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.3s ease;
    justify-self: start;
}

.edit-details-btn:hover {
    background: #5a6fd8;
}

/* Address Styles */
.addresses-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.address-item {
    border: 1px solid #eee;
    border-radius: 10px;
    padding: 1.5rem;
}

.address-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.address-header h4 {
    color: #333;
}

.address-default {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.address-item p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.address-actions {
    display: flex;
    gap: 1rem;
}

.edit-address-btn, .delete-address-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.edit-address-btn {
    background: #667eea;
    color: white;
}

.edit-address-btn:hover {
    background: #5a6fd8;
}

.delete-address-btn {
    background: #ff4757;
    color: white;
}

.delete-address-btn:hover {
    background: #ff3838;
}

.add-address-btn {
    background: #f8f9fa;
    border: 2px dashed #ccc;
    color: #666;
    padding: 1.5rem;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.add-address-btn:hover {
    border-color: #667eea;
    color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

/* Settings Styles */
.settings-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border: 1px solid #eee;
    border-radius: 10px;
}

.setting-item.danger {
    border-color: #ff4757;
    background: rgba(255, 71, 87, 0.05);
}

.setting-info h4 {
    color: #333;
    margin-bottom: 0.5rem;
}

.setting-info p {
    color: #666;
    font-size: 0.9rem;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #667eea;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.setup-2fa-btn, .delete-account-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.setup-2fa-btn {
    background: #667eea;
    color: white;
}

.setup-2fa-btn:hover {
    background: #5a6fd8;
}

.delete-account-btn {
    background: #ff4757;
    color: white;
}

.delete-account-btn:hover {
    background: #ff3838;
}

/* Authentication Page Styles */
.auth-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.auth-main {
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.auth-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.auth-card {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-header h1 {
    color: #333;
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: #666;
    font-size: 1rem;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    left: 1rem;
    color: #666;
    z-index: 1;
}

.input-group input, .input-group select {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.input-group input:focus, .input-group select:focus {
    outline: none;
    border-color: #667eea;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 0.5rem;
}

.password-strength {
    margin-top: 0.5rem;
}

.strength-bar {
    height: 4px;
    background: #e1e8ed;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.25rem;
}

.strength-fill {
    height: 100%;
    background: #ff4757;
    width: 0%;
    transition: all 0.3s ease;
}

.strength-text {
    font-size: 0.8rem;
    color: #666;
}

.form-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: #666;
}

.checkbox-container input {
    margin-right: 0.5rem;
}

.checkbox-container a {
    color: #667eea;
    text-decoration: none;
}

.checkbox-container a:hover {
    text-decoration: underline;
}

.forgot-password {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
    margin-left: auto;
}

.forgot-password:hover {
    text-decoration: underline;
}

.auth-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.auth-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.auth-divider {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
    color: #666;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e1e8ed;
}

.auth-divider span {
    background: white;
    padding: 0 1rem;
}

.social-login {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    background: white;
    color: #333;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.social-btn:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.social-btn.google i {
    color: #db4437;
}

.social-btn.facebook i {
    color: #4267B2;
}

.auth-footer {
    text-align: center;
    margin-top: 1.5rem;
    color: #666;
}

.auth-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    text-decoration: underline;
}

.auth-benefits {
    color: white;
}

.auth-benefits h3 {
    font-size: 1.8rem;
    margin-bottom: 2rem;
    text-align: center;
}

.benefits-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.benefit-item i {
    font-size: 2rem;
    color: #ffd700;
}

.benefit-item h4 {
    margin-bottom: 0.25rem;
    font-size: 1.1rem;
}

.benefit-item p {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Responsive Design Updates */
@media (max-width: 1024px) {
    .auth-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .auth-benefits {
        order: -1;
    }

    .profile-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .profile-sidebar {
        position: static;
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 280px;
        left: -280px;
    }

    .nav-actions {
        gap: 0.5rem;
    }

    .nav-cart, .nav-profile {
        padding: 0.25rem;
    }

    .cart-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .cart-summary {
        position: static;
    }

    .profile-tabs {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1;
        min-width: 120px;
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .profile-stats {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .order-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .order-product {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .address-actions {
        flex-direction: column;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .auth-card {
        padding: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .benefits-list {
        gap: 1rem;
    }

    .benefit-item {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .sidebar {
        width: 100%;
        left: -100%;
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .cart-items-section, .summary-card, .promo-section {
        padding: 1.5rem;
    }

    .cart-actions {
        gap: 0.75rem;
    }

    .continue-shopping-btn, .checkout-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .profile-card {
        padding: 1.5rem;
    }

    .profile-stats {
        padding: 0.75rem 0;
    }

    .tab-content {
        padding: 1.5rem;
    }

    .order-item {
        padding: 1rem;
    }

    .auth-main {
        padding: 100px 0 60px;
    }

    .auth-container {
        padding: 0 15px;
    }

    .auth-card {
        padding: 1.5rem;
    }

    .auth-header h1 {
        font-size: 1.5rem;
    }

    .auth-benefits h3 {
        font-size: 1.5rem;
    }
}
