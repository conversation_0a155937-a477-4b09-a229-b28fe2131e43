// Profile Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeProfilePage();
});

function initializeProfilePage() {
    setupProfileTabs();
    setupProfileEventListeners();
    loadUserData();
}

function setupProfileTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // Remove active class from all tabs and panes
            tabBtns.forEach(b => b.classList.remove('active'));
            tabPanes.forEach(p => p.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding pane
            this.classList.add('active');
            const targetPane = document.getElementById(targetTab);
            if (targetPane) {
                targetPane.classList.add('active');
            }
        });
    });
}

function setupProfileEventListeners() {
    // Edit profile button
    const editProfileBtn = document.querySelector('.edit-profile-btn');
    if (editProfileBtn) {
        editProfileBtn.addEventListener('click', function() {
            alert('Edit profile functionality would be implemented here.');
        });
    }
    
    // Edit details button
    const editDetailsBtn = document.querySelector('.edit-details-btn');
    if (editDetailsBtn) {
        editDetailsBtn.addEventListener('click', function() {
            toggleEditMode();
        });
    }
    
    // Address buttons
    const editAddressBtns = document.querySelectorAll('.edit-address-btn');
    editAddressBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            alert('Edit address functionality would be implemented here.');
        });
    });
    
    const deleteAddressBtns = document.querySelectorAll('.delete-address-btn');
    deleteAddressBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            if (confirm('Are you sure you want to delete this address?')) {
                this.closest('.address-item').remove();
            }
        });
    });
    
    const addAddressBtn = document.querySelector('.add-address-btn');
    if (addAddressBtn) {
        addAddressBtn.addEventListener('click', function() {
            alert('Add new address functionality would be implemented here.');
        });
    }
    
    // Settings buttons
    const setup2faBtn = document.querySelector('.setup-2fa-btn');
    if (setup2faBtn) {
        setup2faBtn.addEventListener('click', function() {
            alert('Two-factor authentication setup would be implemented here.');
        });
    }
    
    const deleteAccountBtn = document.querySelector('.delete-account-btn');
    if (deleteAccountBtn) {
        deleteAccountBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
                alert('Account deletion would be implemented here.');
            }
        });
    }
    
    // Toggle switches
    const toggleSwitches = document.querySelectorAll('.toggle-switch input');
    toggleSwitches.forEach(toggle => {
        toggle.addEventListener('change', function() {
            const setting = this.closest('.setting-item').querySelector('h4').textContent;
            const status = this.checked ? 'enabled' : 'disabled';
            console.log(`${setting} ${status}`);
            // In a real app, you would save this preference to the server
        });
    });
}

function loadUserData() {
    // In a real application, this would load user data from an API
    // For now, we'll use static data that's already in the HTML
    
    // Update cart count in sidebar if it exists
    updateCartUI();
    
    // Load any dynamic content
    loadRecentActivity();
}

function loadRecentActivity() {
    // This would typically load recent user activity from an API
    // For demo purposes, we'll just log that it would be loaded
    console.log('Loading recent user activity...');
}

function toggleEditMode() {
    const form = document.querySelector('.details-form');
    const inputs = form.querySelectorAll('input');
    const editBtn = document.querySelector('.edit-details-btn');
    
    const isReadonly = inputs[0].hasAttribute('readonly');
    
    if (isReadonly) {
        // Enable editing
        inputs.forEach(input => {
            input.removeAttribute('readonly');
            input.style.background = 'white';
            input.style.color = '#333';
        });
        editBtn.textContent = 'Save Changes';
        editBtn.style.background = '#28a745';
    } else {
        // Save and disable editing
        inputs.forEach(input => {
            input.setAttribute('readonly', true);
            input.style.background = '#f8f9fa';
            input.style.color = '#666';
        });
        editBtn.textContent = 'Edit Details';
        editBtn.style.background = '#667eea';
        
        // In a real app, you would save the data to the server here
        alert('Profile details saved successfully!');
    }
}

// Order tracking functionality
function trackOrder(orderNumber) {
    alert(`Tracking information for order ${orderNumber} would be displayed here.`);
}

// Reorder functionality
function reorderItems(orderNumber) {
    if (confirm(`Would you like to reorder all items from order ${orderNumber}?`)) {
        alert('Items have been added to your cart!');
        // In a real app, you would add the items to the cart
    }
}

// Download invoice functionality
function downloadInvoice(orderNumber) {
    alert(`Invoice for order ${orderNumber} would be downloaded here.`);
}

// Add these functions to order items if needed
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('track-order-btn')) {
        const orderNumber = e.target.closest('.order-item').querySelector('.order-number').textContent;
        trackOrder(orderNumber);
    }
    
    if (e.target.classList.contains('reorder-btn')) {
        const orderNumber = e.target.closest('.order-item').querySelector('.order-number').textContent;
        reorderItems(orderNumber);
    }
    
    if (e.target.classList.contains('download-invoice-btn')) {
        const orderNumber = e.target.closest('.order-item').querySelector('.order-number').textContent;
        downloadInvoice(orderNumber);
    }
});

// Profile statistics update
function updateProfileStats() {
    // This would typically calculate stats from user data
    const stats = {
        orders: 12,
        totalSpent: 1247.50,
        status: 'Gold'
    };
    
    const statNumbers = document.querySelectorAll('.stat-number');
    if (statNumbers.length >= 3) {
        statNumbers[0].textContent = stats.orders;
        statNumbers[1].textContent = `$${stats.totalSpent.toFixed(0)}`;
        statNumbers[2].textContent = stats.status;
    }
}

// Initialize profile stats
updateProfileStats();

// Wishlist functionality (if implemented)
function addToWishlist(productId) {
    console.log(`Adding product ${productId} to wishlist`);
    // Implementation would go here
}

function removeFromWishlist(productId) {
    console.log(`Removing product ${productId} from wishlist`);
    // Implementation would go here
}

// Export functions for global use
window.trackOrder = trackOrder;
window.reorderItems = reorderItems;
window.downloadInvoice = downloadInvoice;
window.addToWishlist = addToWishlist;
window.removeFromWishlist = removeFromWishlist;
